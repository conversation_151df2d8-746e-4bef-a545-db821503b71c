#!/usr/bin/env python3
"""
PDF Filename Translator V2 - Improved Consistency

This version ensures consistent translation of filename prefixes across batches.
Same prefixes (like Page01, Page02) will be translated consistently.

Author: AI Assistant
Date: 2025-07-31
"""

import os
import re
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import requests
import time
from urllib.parse import urljoin
from collections import defaultdict
import concurrent.futures
import threading


class ConfigParser:
    """Parse configuration from config.txt file."""
    
    def __init__(self, config_file: str = "config.txt"):
        self.config_file = config_file
        self.config = {}
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from file."""
        try:
            if not os.path.exists(self.config_file):
                raise FileNotFoundError(f"Configuration file {self.config_file} not found")
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and ':' in line:
                        key, value = line.split(':', 1)
                        self.config[key.strip()] = value.strip()
                    elif line and not line.startswith('#'):
                        logging.warning(f"Invalid config line {line_num}: {line}")
            
            # Validate required configuration
            required_keys = ['base_url', 'api', 'model']
            missing_keys = [key for key in required_keys if key not in self.config]
            if missing_keys:
                raise ValueError(f"Missing required configuration keys: {missing_keys}")
                
            logging.info(f"Configuration loaded successfully from {self.config_file}")
            
        except Exception as e:
            logging.error(f"Failed to load configuration: {e}")
            raise
    
    def get(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get configuration value."""
        return self.config.get(key, default)


class FilenameAnalyzer:
    """Analyze filenames to identify patterns and prefixes."""
    
    def __init__(self):
        self.prefix_patterns = [
            r'^(Page\d+)_(.+)$',      # Page01_, Page02_, etc.
            r'^(第\d+页)_(.+)$',      # 第01页_, 第02页_, etc.
            r'^(页\d+)_(.+)$',        # 页01_, 页02_, etc.
            r'^(P\d+)_(.+)$',         # P1_, P2_, etc.
        ]

        # Standard prefix translation mapping for consistency
        self.standard_prefix_map = {
            'page': '第{num}页',  # Page01 -> 第01页, Page02 -> 第02页
            'p': '页{num}',       # P1 -> 页1, P2 -> 页2
        }
    
    def extract_prefix_and_content(self, filename: str) -> Tuple[str, str]:
        """Extract prefix and main content from filename."""
        for pattern in self.prefix_patterns:
            match = re.match(pattern, filename, re.IGNORECASE)
            if match:
                return match.group(1), match.group(2)
        return "", filename
    
    def analyze_batch(self, filenames: List[str]) -> Dict[str, List[str]]:
        """Analyze a batch of filenames and group by prefix patterns."""
        prefix_groups = defaultdict(list)

        for filename in filenames:
            prefix, _ = self.extract_prefix_and_content(filename)
            if prefix:
                # Normalize prefix for grouping (case-insensitive)
                normalized_prefix = prefix.lower()
                prefix_groups[normalized_prefix].append(filename)
            else:
                prefix_groups["no_prefix"].append(filename)

        return dict(prefix_groups)

    def get_standard_prefix_translation(self, prefix: str) -> Optional[str]:
        """Get standardized prefix translation based on predefined rules."""
        prefix_lower = prefix.lower()

        # Extract number from prefix
        number_match = re.search(r'\d+', prefix)
        if not number_match:
            return None

        number = number_match.group()

        # Determine prefix type and apply standard translation
        if prefix_lower.startswith('page'):
            return self.standard_prefix_map['page'].format(num=number)
        elif prefix_lower.startswith('p') and not prefix_lower.startswith('page'):
            return self.standard_prefix_map['p'].format(num=number)

        return None


class ConsistentAITranslator:
    """AI translator with consistency for prefix translations and concurrent processing."""

    def __init__(self, config: ConfigParser, max_workers: int = 5):
        self.base_url = config.get('base_url')
        self.api_key = config.get('api')
        self.model = config.get('model')
        self.max_workers = max_workers

        # Create session template for thread safety
        self.session_headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        # Rate limiting (shared across threads)
        self.rate_limiter = threading.Lock()
        self.last_request_time = 0
        self.min_request_interval = 0.3  # Faster for concurrent processing

        # Consistency tracking
        self.prefix_translations = {}  # Cache for consistent prefix translations
        self.analyzer = FilenameAnalyzer()

        logging.info(f"Consistent AI Translator initialized with model: {self.model}, max_workers: {max_workers}")
    
    def _create_session(self) -> requests.Session:
        """Create a new session for thread safety."""
        session = requests.Session()
        session.headers.update(self.session_headers)
        return session

    def _wait_for_rate_limit(self) -> None:
        """Ensure we don't exceed rate limits (thread-safe)."""
        with self.rate_limiter:
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.min_request_interval:
                sleep_time = self.min_request_interval - time_since_last
                time.sleep(sleep_time)
            self.last_request_time = time.time()
    
    def establish_prefix_translations(self, filenames: List[str]) -> None:
        """Establish consistent prefix translations using predefined rules."""
        print("Analyzing filename patterns for consistency...")

        # Group filenames by prefix
        prefix_groups = self.analyzer.analyze_batch(filenames)

        # For each unique prefix, establish a standardized translation
        for normalized_prefix, files in prefix_groups.items():
            if normalized_prefix == "no_prefix":
                continue

            # Get the actual prefix from the first file in the group
            actual_prefix, _ = self.analyzer.extract_prefix_and_content(files[0])

            if actual_prefix and normalized_prefix not in self.prefix_translations:
                print(f"Establishing translation for prefix: {actual_prefix} (found in {len(files)} files)")

                # Use standardized translation first
                standard_translation = self.analyzer.get_standard_prefix_translation(actual_prefix)
                if standard_translation:
                    self.prefix_translations[normalized_prefix] = standard_translation
                    print(f"  ✅ {actual_prefix} -> {standard_translation} (standardized)")
                else:
                    # Fallback to AI translation for non-standard prefixes
                    translated_prefix = self._translate_prefix_only(actual_prefix)
                    if translated_prefix:
                        self.prefix_translations[normalized_prefix] = translated_prefix
                        print(f"  ✅ {actual_prefix} -> {translated_prefix} (AI translated)")
                    else:
                        print(f"  ❌ Failed to translate prefix: {actual_prefix}")

        print(f"Established {len(self.prefix_translations)} consistent prefix translations")
    
    def _translate_prefix_only(self, prefix: str, max_retries: int = 3) -> Optional[str]:
        """Translate only a prefix to establish consistency."""
        self._wait_for_rate_limit()
        session = self._create_session()

        prompt = f"""Please translate this filename prefix to Chinese. This is a page number prefix that appears in many files.
Keep it short and consistent. Only return the translated prefix, nothing else.

Prefix to translate: {prefix}

Examples:
- Page01 -> 第01页
- Page02 -> 第02页
- P1 -> 页1"""

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are translating filename prefixes. Be consistent and concise. Return only the translated prefix."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 20,
            "temperature": 0.1
        }

        for attempt in range(max_retries):
            try:
                url = urljoin(self.base_url.rstrip('/') + '/', 'chat/completions')
                response = session.post(url, json=payload, timeout=30)
                response.raise_for_status()

                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    translated = result['choices'][0]['message']['content'].strip()
                    cleaned = self._clean_filename(translated)
                    if cleaned:
                        return cleaned

            except Exception as e:
                logging.error(f"Error translating prefix (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)

        return None
    
    def translate_filename(self, filename: str, max_retries: int = 3) -> Optional[str]:
        """Translate filename using established prefix consistency."""
        # Extract prefix and content
        prefix, main_content = self.analyzer.extract_prefix_and_content(filename)

        if prefix:
            normalized_prefix = prefix.lower()
            if normalized_prefix in self.prefix_translations:
                # Use consistent prefix + translate content
                consistent_prefix = self.prefix_translations[normalized_prefix]
                translated_content = self._translate_content(main_content, max_retries)
                if translated_content:
                    result = f"{consistent_prefix}_{translated_content}"
                    logging.info(f"Translated '{filename}' -> '{result}' (using consistent prefix)")
                    return result

        # Fallback: translate the whole filename
        translated = self._translate_content(filename, max_retries)
        if translated:
            logging.info(f"Translated '{filename}' -> '{translated}' (full translation)")
        return translated

    def translate_filenames_batch(self, filenames: List[str]) -> Dict[str, Optional[str]]:
        """Translate multiple filenames concurrently."""
        results = {}

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all translation tasks
            future_to_filename = {
                executor.submit(self.translate_filename, filename): filename
                for filename in filenames
            }

            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_filename):
                filename = future_to_filename[future]
                try:
                    result = future.result()
                    results[filename] = result
                except Exception as e:
                    logging.error(f"Error translating {filename}: {e}")
                    results[filename] = None

        return results
    
    def _translate_content(self, content: str, max_retries: int = 3) -> Optional[str]:
        """Translate content using the AI API."""
        self._wait_for_rate_limit()
        session = self._create_session()

        prompt = f"""Please translate the following text to Chinese.
Keep it concise and suitable for use as a filename.
Only return the translated text, nothing else.

Text to translate: {content}"""

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a professional translator. Translate to Chinese, keeping it concise and filename-safe."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 100,
            "temperature": 0.2
        }

        for attempt in range(max_retries):
            try:
                url = urljoin(self.base_url.rstrip('/') + '/', 'chat/completions')
                response = session.post(url, json=payload, timeout=30)
                response.raise_for_status()

                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    translated = result['choices'][0]['message']['content'].strip()
                    return self._clean_filename(translated)

            except Exception as e:
                logging.error(f"Error translating content (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)

        return None
    
    def _clean_filename(self, filename: str) -> str:
        """Clean filename to ensure it's valid for the file system."""
        cleaned = filename.strip().strip('"\'')
        invalid_chars = r'[<>:"/\\|?*]'
        cleaned = re.sub(invalid_chars, '_', cleaned)
        cleaned = re.sub(r'[\x00-\x1f\x7f]', '', cleaned)
        
        if len(cleaned) > 200:
            cleaned = cleaned[:200].strip()
        
        cleaned = cleaned.strip('. ')
        return cleaned if cleaned else "translated_file"


class PDFProcessor:
    """Handle PDF file operations with consistent translation."""

    def __init__(self, pdf_folder: str = "pdf", output_folder: str = "translated_pdfs"):
        self.pdf_folder = Path(pdf_folder)
        self.output_folder = Path(output_folder)
        self.translator = None

        if not self.pdf_folder.exists():
            raise FileNotFoundError(f"PDF folder not found: {self.pdf_folder}")

        self.output_folder.mkdir(exist_ok=True)
        logging.info(f"PDF Processor initialized - Input: {self.pdf_folder}, Output: {self.output_folder}")

    def set_translator(self, translator: ConsistentAITranslator) -> None:
        """Set the AI translator instance."""
        self.translator = translator

    def scan_pdf_files(self) -> List[Path]:
        """Scan the PDF folder for all PDF files."""
        # Use case-insensitive pattern to avoid duplicates
        pdf_files = []

        # Collect all PDF files (case-insensitive)
        for file_path in self.pdf_folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() == '.pdf':
                pdf_files.append(file_path)

        logging.info(f"Found {len(pdf_files)} PDF files in {self.pdf_folder}")
        return sorted(pdf_files)

    def process_files(self, pdf_files: List[Path]) -> Dict[str, str]:
        """Process PDF files with consistent translation and concurrent processing."""
        if not self.translator:
            raise ValueError("Translator not set. Call set_translator() first.")

        # Extract filenames for analysis
        filenames = [pdf_file.stem for pdf_file in pdf_files]

        # Establish consistent prefix translations first
        self.translator.establish_prefix_translations(filenames)

        print(f"\nStarting concurrent translation of {len(pdf_files)} files...")

        # Translate all filenames concurrently
        translation_results = self.translator.translate_filenames_batch(filenames)

        # Process file copying
        results = {}
        successful_translations = 0

        for i, pdf_file in enumerate(pdf_files, 1):
            print(f"Processing file {i}/{len(pdf_files)}: {pdf_file.name}")

            try:
                original_name = pdf_file.stem
                extension = pdf_file.suffix
                translated_name = translation_results.get(original_name)

                if translated_name:
                    new_filename = f"{translated_name}{extension}"
                    new_file_path = self.output_folder / new_filename

                    # Handle filename conflicts
                    counter = 1
                    while new_file_path.exists():
                        new_filename = f"{translated_name}_{counter}{extension}"
                        new_file_path = self.output_folder / new_filename
                        counter += 1

                    shutil.copy2(pdf_file, new_file_path)

                    results[pdf_file.name] = new_filename
                    successful_translations += 1

                    print(f"  ✅ {pdf_file.name} -> {new_filename}")
                else:
                    print(f"  ❌ Failed to translate: {pdf_file.name}")
                    results[pdf_file.name] = "TRANSLATION_FAILED"

            except Exception as e:
                print(f"  ❌ Error processing {pdf_file.name}: {e}")
                results[pdf_file.name] = f"ERROR: {str(e)}"

        print(f"\nProcessing complete. Successfully translated {successful_translations}/{len(pdf_files)} files.")
        return results


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('pdf_translator_v2.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


def print_results(results: Dict[str, str]) -> None:
    """Print processing results in a formatted way."""
    print("\n" + "="*80)
    print("TRANSLATION RESULTS")
    print("="*80)

    successful = 0
    failed = 0

    for original, translated in results.items():
        if translated.startswith("ERROR:") or translated == "TRANSLATION_FAILED":
            print(f"❌ {original} -> {translated}")
            failed += 1
        else:
            print(f"✅ {original} -> {translated}")
            successful += 1

    print("\n" + "-"*80)
    print(f"Summary: {successful} successful, {failed} failed out of {len(results)} total files")
    print("-"*80)


def main():
    """Main function to run the improved PDF filename translator."""
    try:
        setup_logging()

        print("PDF Filename Translator V2 - Improved Consistency")
        print("="*60)

        # Load configuration
        print("Loading configuration...")
        config = ConfigParser()
        print(f"✅ Configuration loaded - Model: {config.get('model')}")

        # Initialize AI translator with concurrent processing
        print("Initializing consistent AI translator with concurrent processing...")
        translator = ConsistentAITranslator(config, max_workers=5)
        print("✅ Consistent AI translator initialized with 5 concurrent workers")

        # Initialize PDF processor
        print("Initializing PDF processor...")
        processor = PDFProcessor()
        processor.set_translator(translator)
        print("✅ PDF processor initialized")

        # Scan for PDF files
        print("Scanning for PDF files...")
        pdf_files = processor.scan_pdf_files()

        if not pdf_files:
            print("❌ No PDF files found in the 'pdf' folder.")
            return

        print(f"✅ Found {len(pdf_files)} PDF files")

        # Ask for confirmation
        print(f"\nReady to translate {len(pdf_files)} PDF filenames to Chinese with consistent prefixes.")
        response = input("Continue? (y/N): ").strip().lower()

        if response != 'y':
            print("Operation cancelled.")
            return

        # Process files
        results = processor.process_files(pdf_files)

        # Print results
        print_results(results)

        print(f"\nTranslated files saved to: {processor.output_folder}")
        print("Process completed successfully!")

    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
    except Exception as e:
        logging.error(f"Fatal error: {e}")
        print(f"\n❌ Fatal error: {e}")
        print("Check pdf_translator_v2.log for detailed error information.")


if __name__ == "__main__":
    main()
